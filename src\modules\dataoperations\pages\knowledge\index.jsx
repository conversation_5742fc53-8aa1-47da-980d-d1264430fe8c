import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Layout,
  Tabs,
  Card,
  Button,
  Input,
  Table,
  Space,
  Pagination,
  Upload,
  Modal,
  Form,
  Select,
  Radio,
  Slider,
  Divider,
  Alert,
  Progress,
  Tag,
  Empty,
  message,
  Spin,
  Tree,
  InputNumber
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  UploadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  EditOutlined,
  SettingOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  FileTextOutlined,
  FolderOutlined
} from '@ant-design/icons';
import {
  knowledgeList,
  knowledgeCreate,
  knowledgeUpdate,
  knowledgeDelete,
  documentPage,
  documentUpload,
  documentDownload,
  documentDelete,
  knowledgeDocuments,
  bindDocuments,
  unbindDocument,
  processDocument,
  initializeKnowledge,
  getInitializeStatus,
  categoryTree,
  categoryCreate,
  categoryUpdate,
  categoryDelete,
  getMilvusCollections
} from '../../api/knowledge';
import {
  CategoryModal,
  KnowledgeModal,
  UploadModal
} from './components/KnowledgeModals';
import {
  BindDocumentsModal,
  InitializeModal
} from './components/AdvancedModals';
import css from './index.module.less';

const { Content, Sider } = Layout;
const { TabPane } = Tabs;
const { Search } = Input;
const { Option } = Select;

/**
 * 知识库管理主页面组件
 * <AUTHOR>
 */
const KnowledgeManagement = () => {
  // 基础状态
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('dataManagement');
  
  // 类目相关状态
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [treeLoading, setTreeLoading] = useState(false);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  
  // 知识库相关状态
  const [knowledgeBases, setKnowledgeBases] = useState([]);
  const [selectedKnowledge, setSelectedKnowledge] = useState(null);
  const [knowledgeSearchKeyword, setKnowledgeSearchKeyword] = useState('');
  const [filteredKnowledgeBases, setFilteredKnowledgeBases] = useState([]);
  
  // 文档相关状态
  const [documents, setDocuments] = useState([]);
  const [documentTotal, setDocumentTotal] = useState(0);
  const [documentPageConfig, setDocumentPageConfig] = useState({
    current: 1,
    pageSize: 10
  });
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filterType, setFilterType] = useState('');
  
  // 对话框状态
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  const [knowledgeModalVisible, setKnowledgeModalVisible] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [bindDocModalVisible, setBindDocModalVisible] = useState(false);
  const [initializeModalVisible, setInitializeModalVisible] = useState(false);
  
  // 表单实例
  const [categoryForm] = Form.useForm();
  const [knowledgeForm] = Form.useForm();
  const [uploadForm] = Form.useForm();
  
  // 其他状态
  const [editingCategory, setEditingCategory] = useState(null);
  const [editingKnowledge, setEditingKnowledge] = useState(null);
  const [fileList, setFileList] = useState([]);
  const [collections, setCollections] = useState([]);
  const [availableDocuments, setAvailableDocuments] = useState([]);
  const [selectedDocuments, setSelectedDocuments] = useState([]);
  const [knowledgeDocList, setKnowledgeDocList] = useState([]);
  const [initializeProgress, setInitializeProgress] = useState({
    visible: false,
    taskId: '',
    status: '',
    totalDocuments: 0,
    successCount: 0,
    failedCount: 0,
    errorMessage: ''
  });
  
  const initializeTimer = useRef(null);

  /**
   * 加载类目树数据
   * <AUTHOR>
   */
  const loadCategoryTree = useCallback(async () => {
    setTreeLoading(true);
    try {
      // 获取类目树需要tenantId参数
      const response = await categoryTree({ tenantId: 15 }); // 默认租户ID，实际应该从用户信息中获取
      console.log('类目树API响应:', response);

      if (response.code === "200" || response.code === 200) {
        const data = response.data || [];
        console.log('类目数据:', data);
        const treeData = formatTreeData(data);
        setCategories(treeData);
        if (treeData.length > 0 && !selectedCategory) {
          setSelectedCategory(treeData[0]);
          setSelectedKeys([treeData[0].key]);
        }
      } else {
        console.error('类目API返回错误:', response);
        message.error(response.message || '加载类目失败');
      }
    } catch (error) {
      console.error('加载类目错误:', error);
      message.error('加载类目失败');
    } finally {
      setTreeLoading(false);
    }
  }, [selectedCategory]);

  /**
   * 格式化树形数据
   * @param {Array} data - 原始数据
   * @returns {Array} 格式化后的树形数据
   * <AUTHOR>
   */
  const formatTreeData = (data) => {
    return data.map(item => ({
      key: item.id,
      title: item.name,
      icon: <FolderOutlined />,
      children: item.children ? formatTreeData(item.children) : undefined,
      ...item
    }));
  };

  /**
   * 加载知识库列表
   * <AUTHOR>
   */
  const loadKnowledgeList = useCallback(async () => {
    setLoading(true);
    try {
      const response = await knowledgeList({});
      console.log('知识库列表API响应:', response);

      if (response.code === "200" || response.code === 200) {
        const data = response.data || [];
        console.log('知识库数据:', data);
        setKnowledgeBases(data);
      } else {
        console.error('知识库API返回错误:', response);
        message.error(response.message || '加载知识库列表失败');
      }
    } catch (error) {
      console.error('加载知识库列表错误:', error);
      message.error('加载知识库列表失败');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 加载文档列表
   * <AUTHOR>
   */
  const loadDocumentList = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        current: documentPageConfig.current,
        size: documentPageConfig.pageSize
      };

      if (selectedCategory) {
        params.categoryId = selectedCategory.key;
      }
      if (searchKeyword) {
        params.fileName = searchKeyword;
      }
      if (filterType) {
        params.fileType = filterType;
      }

      console.log('文档列表请求参数:', params);
      const response = await documentPage(params);
      console.log('文档列表API响应:', response);

      if (response.code === "200" || response.code === 200) {
        const records = response.data?.records || [];
        console.log('文档记录数量:', records.length);
        setDocuments(records);
        setDocumentTotal(response.data?.total || 0);
      } else {
        console.error('API返回错误:', response);
        message.error(response.message || '加载文档列表失败');
      }
    } catch (error) {
      console.error('加载文档列表错误:', error);
      message.error('加载文档列表失败');
    } finally {
      setLoading(false);
    }
  }, [documentPageConfig, selectedCategory, searchKeyword, filterType]);

  /**
   * 搜索知识库
   * <AUTHOR>
   */
  const searchKnowledgeBases = useCallback(() => {
    if (!knowledgeSearchKeyword) {
      setFilteredKnowledgeBases(knowledgeBases);
    } else {
      const filtered = knowledgeBases.filter(kb =>
        kb.name.toLowerCase().includes(knowledgeSearchKeyword.toLowerCase())
      );
      setFilteredKnowledgeBases(filtered);
    }
  }, [knowledgeBases, knowledgeSearchKeyword]);

  // 初始化数据加载
  useEffect(() => {
    loadCategoryTree();
    loadKnowledgeList();
  }, [loadCategoryTree, loadKnowledgeList]);

  // 文档列表数据加载
  useEffect(() => {
    if (activeTab === 'dataManagement') {
      loadDocumentList();
    }
  }, [activeTab, loadDocumentList]);

  // 知识库搜索
  useEffect(() => {
    searchKnowledgeBases();
  }, [searchKnowledgeBases]);

  /**
   * 选择类目
   * @param {Array} selectedKeys - 选中的key数组
   * @param {Object} info - 选择信息
   * <AUTHOR>
   */
  const onSelectCategory = (selectedKeys, info) => {
    if (selectedKeys.length > 0) {
      setSelectedKeys(selectedKeys);
      setSelectedCategory(info.node);
      setDocumentPageConfig(prev => ({ ...prev, current: 1 }));
      // 触发文档列表重新加载
      loadDocumentList();
    }
  };

  /**
   * 选择知识库
   * @param {Object} knowledge - 知识库对象
   * <AUTHOR>
   */
  const selectKnowledge = (knowledge) => {
    setSelectedKnowledge(knowledge);
    loadKnowledgeDocuments(knowledge.id);
  };

  /**
   * 加载知识库文档
   * @param {string} knowledgeId - 知识库ID
   * <AUTHOR>
   */
  const loadKnowledgeDocuments = async (knowledgeId) => {
    try {
      const response = await knowledgeDocuments({ knowledgeId });
      console.log('知识库文档API响应:', response);

      if (response.code === "200" || response.code === 200) {
        const data = response.data || [];
        console.log('知识库文档数据:', data);
        setKnowledgeDocList(data);
      } else {
        console.error('知识库文档API返回错误:', response);
        message.error(response.message || '加载知识库文档失败');
      }
    } catch (error) {
      console.error('加载知识库文档错误:', error);
      message.error('加载知识库文档失败');
    }
  };

  /**
   * 删除知识库
   * @param {Object} knowledge - 知识库对象
   * <AUTHOR>
   */
  const handleDeleteKnowledge = (knowledge) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除知识库"${knowledge.name}"吗？删除后将无法恢复。`,
      onOk: async () => {
        try {
          await knowledgeDelete(knowledge.id);
          message.success('删除知识库成功');
          if (selectedKnowledge?.id === knowledge.id) {
            setSelectedKnowledge(null);
            setKnowledgeDocList([]);
          }
          loadKnowledgeList();
        } catch (error) {
          message.error('删除知识库失败');
        }
      }
    });
  };

  /**
   * 解绑文档
   * @param {Object} document - 文档对象
   * <AUTHOR>
   */
  const handleUnbindDocument = (document) => {
    Modal.confirm({
      title: '确认解绑',
      content: `确定要解绑文档"${document.fileName}"吗？`,
      onOk: async () => {
        try {
          await unbindDocument({
            knowledgeId: selectedKnowledge.id,
            documentId: document.id
          });
          message.success('解绑文档成功');
          loadKnowledgeDocuments(selectedKnowledge.id);
        } catch (error) {
          message.error('解绑文档失败');
        }
      }
    });
  };

  /**
   * 重新处理文档
   * @param {Object} document - 文档对象
   * <AUTHOR>
   */
  const handleProcessDocument = async (document) => {
    try {
      await processDocument({
        knowledgeId: selectedKnowledge.id,
        documentId: document.id
      });
      message.success('开始重新处理文档');
      loadKnowledgeDocuments(selectedKnowledge.id);
    } catch (error) {
      message.error('处理文档失败');
    }
  };

  return (
    <div className={css.knowledgeManagement}>
      <Layout style={{ height: '100%' }}>
        {/* 左侧边栏 */}
        <Sider width={340} className={css.sidebar}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            tabPosition="top"
            className={css.sidebarTabs}
          >
            <TabPane tab="数据管理" key="dataManagement">
              <div className={css.tabContent}>
                <div className={css.sectionHeader}>
                  <span>类目管理</span>
                  <Button
                    type="text"
                    icon={<PlusOutlined />}
                    size="small"
                    onClick={() => {
                      setEditingCategory(null);
                      categoryForm.resetFields();
                      setCategoryModalVisible(true);
                    }}
                  />
                </div>
                <Spin spinning={treeLoading}>
                  <Tree
                    treeData={categories}
                    selectedKeys={selectedKeys}
                    expandedKeys={expandedKeys}
                    onSelect={onSelectCategory}
                    onExpand={setExpandedKeys}
                    showIcon
                    className={css.categoryTree}
                  />
                </Spin>
              </div>
            </TabPane>

            <TabPane tab="知识库管理" key="knowledgeManagement">
              <div className={css.tabContent}>
                <div className={css.sectionHeader}>
                  <span>知识库列表</span>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    size="small"
                    onClick={() => {
                      setEditingKnowledge(null);
                      knowledgeForm.resetFields();
                      setKnowledgeModalVisible(true);
                    }}
                  >
                    新建
                  </Button>
                </div>

                <Search
                  placeholder="搜索知识库名称"
                  value={knowledgeSearchKeyword}
                  onChange={(e) => setKnowledgeSearchKeyword(e.target.value)}
                  style={{ marginBottom: 16 }}
                  allowClear
                />

                <div className={css.knowledgeList}>
                  {filteredKnowledgeBases.map(knowledge => (
                    <Card
                      key={knowledge.id}
                      size="small"
                      className={`${css.knowledgeCard} ${
                        selectedKnowledge?.id === knowledge.id ? css.active : ''
                      }`}
                      onClick={() => selectKnowledge(knowledge)}
                      actions={[
                        <SettingOutlined key="setting" onClick={(e) => {
                          e.stopPropagation();
                          // 配置知识库
                        }} />,
                        <EditOutlined key="edit" onClick={(e) => {
                          e.stopPropagation();
                          setEditingKnowledge(knowledge);
                          knowledgeForm.setFieldsValue(knowledge);
                          setKnowledgeModalVisible(true);
                        }} />,
                        <DeleteOutlined key="delete" onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteKnowledge(knowledge);
                        }} />
                      ]}
                    >
                      <Card.Meta
                        title={knowledge.name}
                        description={
                          <div>
                            <div className={css.knowledgeDesc}>
                              {knowledge.description}
                            </div>
                            <div className={css.knowledgeMeta}>
                              <span>文档: {knowledge.documentCount || 0}个</span>
                              <span>类型: {knowledge.dataType === '00' ? '非结构化' : '结构化'}</span>
                            </div>
                          </div>
                        }
                      />
                    </Card>
                  ))}
                </div>
              </div>
            </TabPane>
          </Tabs>
        </Sider>

        {/* 右侧内容区域 */}
        <Content className={css.content}>
          {activeTab === 'dataManagement' ? (
            <DataManagementContent
              selectedCategory={selectedCategory}
              documents={documents}
              documentTotal={documentTotal}
              documentPageConfig={documentPageConfig}
              setDocumentPageConfig={setDocumentPageConfig}
              searchKeyword={searchKeyword}
              setSearchKeyword={setSearchKeyword}
              filterType={filterType}
              setFilterType={setFilterType}
              loading={loading}
              onUpload={() => setUploadModalVisible(true)}
              onSearch={loadDocumentList}
              onRefresh={() => {
                setSearchKeyword('');
                setFilterType('');
                setDocumentPageConfig(prev => ({ ...prev, current: 1 }));
                loadDocumentList();
              }}
            />
          ) : (
            <KnowledgeManagementContent
              selectedKnowledge={selectedKnowledge}
              knowledgeDocList={knowledgeDocList}
              onBindDocuments={() => setBindDocModalVisible(true)}
              onInitialize={() => setInitializeModalVisible(true)}
              onUnbindDocument={handleUnbindDocument}
              onProcessDocument={handleProcessDocument}
            />
          )}
        </Content>
      </Layout>

      {/* 对话框组件 */}
      <CategoryModal
        visible={categoryModalVisible}
        onCancel={() => setCategoryModalVisible(false)}
        onSuccess={() => {
          loadCategoryTree();
        }}
        editingCategory={editingCategory}
        form={categoryForm}
        categories={categories}
      />

      <KnowledgeModal
        visible={knowledgeModalVisible}
        onCancel={() => setKnowledgeModalVisible(false)}
        onSuccess={() => {
          loadKnowledgeList();
        }}
        editingKnowledge={editingKnowledge}
        form={knowledgeForm}
      />

      <UploadModal
        visible={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        onSuccess={() => {
          loadDocumentList();
        }}
        categories={categories}
        knowledgeBases={knowledgeBases}
        form={uploadForm}
      />

      <BindDocumentsModal
        visible={bindDocModalVisible}
        onCancel={() => setBindDocModalVisible(false)}
        onSuccess={() => {
          if (selectedKnowledge) {
            loadKnowledgeDocuments(selectedKnowledge.id);
          }
        }}
        selectedKnowledge={selectedKnowledge}
        categories={categories}
      />

      <InitializeModal
        visible={initializeModalVisible}
        onCancel={() => setInitializeModalVisible(false)}
        onSuccess={() => {
          if (selectedKnowledge) {
            loadKnowledgeDocuments(selectedKnowledge.id);
          }
        }}
        selectedKnowledge={selectedKnowledge}
      />
    </div>
  );
};

/**
 * 数据管理内容组件
 * <AUTHOR>
 */
const DataManagementContent = ({
  selectedCategory,
  documents,
  documentTotal,
  documentPageConfig,
  setDocumentPageConfig,
  searchKeyword,
  setSearchKeyword,
  filterType,
  setFilterType,
  loading,
  onUpload,
  onSearch,
  onRefresh
}) => {
  /**
   * 下载文档
   * @param {Object} document - 文档对象
   * <AUTHOR>
   */
  const handleDownload = async (document) => {
    try {
      const response = await documentDownload(document.id);
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', document.fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      message.error('下载文档失败');
    }
  };

  /**
   * 删除文档
   * @param {Object} document - 文档对象
   * <AUTHOR>
   */
  const handleDelete = async (document) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文档"${document.fileName}"吗？`,
      onOk: async () => {
        try {
          await documentDelete(document.id);
          message.success('删除文档成功');
          onSearch();
        } catch (error) {
          message.error('删除文档失败');
        }
      }
    });
  };

  /**
   * 格式化文件大小
   * @param {number} size - 文件大小（字节）
   * @returns {string} 格式化后的大小
   * <AUTHOR>
   */
  const formatFileSize = (size) => {
    if (!size) return '0 B';
    const units = ['B', 'KB', 'MB', 'GB'];
    let index = 0;
    while (size >= 1024 && index < units.length - 1) {
      size /= 1024;
      index++;
    }
    return `${size.toFixed(2)} ${units[index]}`;
  };

  /**
   * 获取状态标签
   * @param {string} status - 状态码
   * @returns {JSX.Element} 状态标签
   * <AUTHOR>
   */
  const getStatusTag = (status) => {
    const statusMap = {
      '00': { color: 'processing', text: '待处理' },
      '01': { color: 'processing', text: '处理中' },
      '02': { color: 'success', text: '处理成功' },
      '03': { color: 'success', text: '已完成' },
      '04': { color: 'error', text: '处理失败' }
    };
    const config = statusMap[status] || { color: 'default', text: '未知状态' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: '文件名称',
      dataIndex: 'fileName',
      key: 'fileName',
      render: (text) => (
        <Space>
          <FileTextOutlined />
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: '文件格式',
      dataIndex: 'fileType',
      key: 'fileType',
      width: 100,
      render: (text) => <Tag>{text?.toUpperCase()}</Tag>
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 120,
      render: (size) => formatFileSize(size)
    },
    {
      title: '状态',
      dataIndex: 'parseStatus',
      key: 'parseStatus',
      width: 120,
      render: (status) => getStatusTag(status)
    },
    {
      title: '标签',
      dataIndex: 'label',
      key: 'label',
      width: 120,
      render: (text) => text ? <Tag color="blue">{text}</Tag> : '-'
    },
    {
      title: '业务信息',
      dataIndex: 'bizInfo',
      key: 'bizInfo',
      width: 120,
      render: (text) => text || '-'
    },
    {
      title: '上传时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (time) => time ? new Date(time).toLocaleString() : ''
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<DownloadOutlined />}
            size="small"
            onClick={() => handleDownload(record)}
          />
          <Button
            type="text"
            icon={<DeleteOutlined />}
            size="small"
            danger
            onClick={() => handleDelete(record)}
          />
        </Space>
      )
    }
  ];

  return (
    <div className={css.dataManagement}>
      {/* 搜索和操作栏 */}
      <Card className={css.searchCard}>
        <div className={css.searchHeader}>
          <div className={css.searchLeft}>
            <Space size="large">
              <span className={css.categoryName}>
                {selectedCategory?.title || '请选择类目'}
              </span>
              <InfoCircleOutlined />
              <span className={css.docStats}>
                共{documentTotal}个文件 | {documents.filter(d => d.parseStatus === '01').length}个文件解析中
              </span>
            </Space>
          </div>

          <Space>
            <Input
              placeholder="搜索文件名称"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              style={{ width: 200 }}
              allowClear
            />
            <Select
              placeholder="全部"
              value={filterType}
              onChange={setFilterType}
              style={{ width: 120 }}
              allowClear
            >
              <Option value="">全部</Option>
              <Option value="pdf">PDF</Option>
              <Option value="docx">Word</Option>
              <Option value="xlsx">Excel</Option>
            </Select>
            <Button icon={<SearchOutlined />} onClick={onSearch}>
              搜索
            </Button>
            <Button icon={<ReloadOutlined />} onClick={onRefresh}>
              刷新
            </Button>
          </Space>
        </div>

        <div className={css.searchRight}>
          <Space>
            <Button type="text">数据解析设置</Button>
            <Button type="text">批量管理</Button>
            <Button type="primary" icon={<UploadOutlined />} onClick={onUpload}>
              导入数据
            </Button>
          </Space>
        </div>
      </Card>

      {/* 文档表格 */}
      <Card className={css.tableCard}>
        <Table
          columns={columns}
          dataSource={documents}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
        />

        <div className={css.pagination}>
          <Pagination
            current={documentPageConfig.current}
            pageSize={documentPageConfig.pageSize}
            total={documentTotal}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`}
            onChange={(current, pageSize) => {
              setDocumentPageConfig({ current, pageSize });
            }}
          />
        </div>
      </Card>
    </div>
  );
};

/**
 * 知识库管理内容组件
 * <AUTHOR>
 */
const KnowledgeManagementContent = ({
  selectedKnowledge,
  knowledgeDocList,
  onBindDocuments,
  onInitialize,
  onUnbindDocument,
  onProcessDocument
}) => {
  if (!selectedKnowledge) {
    return (
      <div className={css.emptyState}>
        <Empty
          description="请选择知识库"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary">创建知识库</Button>
        </Empty>
      </div>
    );
  }

  /**
   * 获取文档状态标签类型
   * @param {string} status - 状态
   * @returns {string} 标签类型
   * <AUTHOR>
   */
  const getDocStatusType = (status) => {
    const typeMap = {
      'processing': 'warning',
      'completed': 'success',
      'failed': 'error'
    };
    return typeMap[status] || 'default';
  };

  /**
   * 获取文档状态文本
   * @param {string} status - 状态
   * @returns {string} 状态文本
   * <AUTHOR>
   */
  const getDocStatusText = (status) => {
    const textMap = {
      'processing': '处理中',
      'completed': '已完成',
      'failed': '处理失败'
    };
    return textMap[status] || '未知状态';
  };

  const columns = [
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
      render: (text) => (
        <Space>
          <FileTextOutlined />
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: '文件类型',
      dataIndex: 'fileType',
      key: 'fileType',
      width: 100,
      render: (text) => <Tag>{text?.toUpperCase()}</Tag>
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => (
        <Tag color={getDocStatusType(status)}>
          {getDocStatusText(status)}
        </Tag>
      )
    },
    {
      title: '切片数量',
      dataIndex: 'chunkCount',
      key: 'chunkCount',
      width: 100
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 180,
      render: (time) => time ? new Date(time).toLocaleString() : ''
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            size="small"
            onClick={() => onProcessDocument(record)}
          >
            重新处理
          </Button>
          <Button
            type="text"
            size="small"
            danger
            onClick={() => onUnbindDocument(record)}
          >
            解绑
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div className={css.knowledgeDetail}>
      {/* 知识库头部信息 */}
      <Card className={css.knowledgeHeader}>
        <div className={css.headerContent}>
          <div className={css.knowledgeInfo}>
            <h2>{selectedKnowledge.name}</h2>
            <div className={css.knowledgeMeta}>
              <span>{knowledgeDocList.length}个文档</span>
              <span>更新时间：{selectedKnowledge.updatedAt ? new Date(selectedKnowledge.updatedAt).toLocaleString() : ''}</span>
            </div>
          </div>
          <div className={css.knowledgeActions}>
            <Space>
              <Button type="primary" onClick={onInitialize}>
                初始化知识库
              </Button>
              <Button>配置</Button>
              <Button>编辑</Button>
              <Button danger>删除</Button>
            </Space>
          </div>
        </div>
      </Card>

      {/* 关联文档列表 */}
      <Card
        className={css.documentsCard}
        title="关联文档"
        extra={
          <Button type="primary" onClick={onBindDocuments}>
            绑定文档
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={knowledgeDocList}
          rowKey="id"
          pagination={false}
        />
      </Card>
    </div>
  );
};

export default KnowledgeManagement;
