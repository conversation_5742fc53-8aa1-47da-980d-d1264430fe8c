import axios from '@/api/fetch';

// ----------知识库相关接口------------

/**
 * 获取知识库列表
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const knowledgeList = (params) => {
  return axios.post('/aio/rag-knowledge/list', params);
};

/**
 * 创建知识库
 * @param {Object} params - 知识库信息
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const knowledgeCreate = (params) => {
  return axios.post('/aio/rag-knowledge/create', params);
};

/**
 * 更新知识库
 * @param {Object} params - 知识库信息
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const knowledgeUpdate = (params) => {
  return axios.post('/aio/rag-knowledge/update', params);
};

/**
 * 删除知识库
 * @param {string} id - 知识库ID
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const knowledgeDelete = (id) => {
  return axios.post(`/aio/rag-knowledge/delete/${id}`);
};

/**
 * 获取知识库详情
 * @param {string} id - 知识库ID
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const knowledgeDetail = (id) => {
  return axios.get(`/aio/rag-knowledge/detail/${id}`);
};

// ----------文档相关接口------------

/**
 * 获取文档列表（分页）
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const documentPage = (params) => {
  return axios.post('/aio/rag-document/page', params);
};

/**
 * 上传文档
 * @param {FormData} formData - 文件数据
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const documentUpload = (formData) => {
  return axios.post('/aio/rag-document/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

/**
 * 下载文档
 * @param {string} id - 文档ID
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const documentDownload = (id) => {
  return axios.get(`/aio/rag-document/download/${id}`, {
    responseType: 'blob'
  });
};

/**
 * 删除文档
 * @param {string} id - 文档ID
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const documentDelete = (id) => {
  return axios.post(`/aio/rag-document/delete/${id}`);
};

/**
 * 获取知识库关联的文档列表
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const knowledgeDocuments = (params) => {
  return axios.post('/aio/rag-knowledge/documents', params);
};

/**
 * 绑定文档到知识库
 * @param {Object} params - 绑定参数
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const bindDocuments = (params) => {
  return axios.post('/aio/rag-knowledge/bind-documents', params);
};

/**
 * 从知识库解绑文档
 * @param {Object} params - 解绑参数
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const unbindDocument = (params) => {
  return axios.post('/aio/rag-knowledge/unbind-document', params);
};

/**
 * 重新处理文档
 * @param {Object} params - 处理参数
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const processDocument = (params) => {
  return axios.post('/aio/rag-knowledge/process-document', params);
};

// ----------知识库初始化相关接口------------

/**
 * 初始化知识库
 * @param {Object} params - 初始化参数
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const initializeKnowledge = (params) => {
  return axios.post('/aio/rag-knowledge/initialize', params);
};

/**
 * 获取初始化状态
 * @param {string} taskId - 任务ID
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const getInitializeStatus = (taskId) => {
  return axios.get(`/aio/rag-knowledge/initialize-status/${taskId}`);
};

/**
 * 取消初始化任务
 * @param {string} taskId - 任务ID
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const cancelInitialize = (taskId) => {
  return axios.post(`/aio/rag-knowledge/cancel-initialize/${taskId}`);
};

// ----------类目相关接口------------

/**
 * 获取类目树
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const categoryTree = (params) => {
  return axios.get('/aio/rag-category/tree', { params });
};

/**
 * 创建类目
 * @param {Object} params - 类目信息
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const categoryCreate = (params) => {
  return axios.post('/aio/rag-category/add', params);
};

/**
 * 更新类目
 * @param {Object} params - 类目信息
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const categoryUpdate = (params) => {
  return axios.post('/aio/rag-category/update', params);
};

/**
 * 删除类目
 * @param {string} id - 类目ID
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const categoryDelete = (id) => {
  return axios.post(`/aio/rag-category/delete/${id}`);
};

// ----------向量数据库相关接口------------

/**
 * 获取Milvus Collections列表
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const getMilvusCollections = () => {
  return axios.get('/aio/milvus/collections');
};

/**
 * 创建Milvus Collection
 * @param {Object} params - Collection参数
 * @returns {Promise} API响应
 * <AUTHOR>
 */
export const createMilvusCollection = (params) => {
  return axios.post('/aio/milvus/collection/create', params);
};
