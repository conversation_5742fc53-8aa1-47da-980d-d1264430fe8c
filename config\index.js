/**
 * @Author: 焦质晔
 * @Date: 2019-06-20 10:00:00
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-04-23 11:14:54
 */
'use strict';
const path = require('path');
const appConf = require('./app.conf');
const envConf = require('./env.conf');

function resolve(dir) {
  return path.resolve(__dirname, '..', dir);
}

// 配置常量
const HOST = 'localhost';
const PORT = 9051;

module.exports = {
  ...appConf,
  theme: envConf.theme,

  // 模块路径别名
  pathAlias: {
    '@': resolve('src'),
    '@test': resolve('src/modules/test'),
    '@framework': resolve('src/modules/framework'),
  },

  // For ModuleFederationPlugin
  /**
   * =============================================
   * exposes 作用：定义当前应用要暴露给其他应用的模块
   * 示例：'./Test': resolve('src/modules/test/pages/widget/Chart2')
   * 格式：'./组件名': resolve('组件路径')
   * =============================================
   * remotes 作用：定义当前应用所依赖的远程模块
   * 示例：'tds_app': `http://localhost:9022/`
   * 格式：'远程应用名称_app': `远程应用地址`
   * 注意：remotes 建议在 `config/env.conf.js` 环境变量中定义
   * =============================================
   */
  moduleFederation: {
    exposes: {},
    remotes: envConf.remotes,
  },

  dev: {
    // Paths
    assetsSubDirectory: 'static',
    assetsPublicPath: `http://${HOST}:${PORT}` + envConf.publicPath,

    // 请求代理配置 -> can be modified
    proxyTable: {
      '/api-dev': {
        target: 'https://uat-iwork.faw.cn',
        changeOrigin: true, // 支持跨域
        secure: false, // 支持 https
        pathRewrite: {
          '^/api-dev': '/api-dev',
        },
      },
      '/aio': {
        target: 'http://localhost:8080',
        changeOrigin: true, // 支持跨域
        secure: false, // 支持 https
        pathRewrite: {
          '^/aio': '/',
        },
      },
      '/kanBan': {
        target: 'https://miw-aio-uat.faw.cn',
        changeOrigin: true, // 支持跨域
        secure: false, // 支持 https
        pathRewrite: {
          '^/kanBan': '/kanBan',
        },
      },
      '/crs': {
        target: 'https://miw-clue-sit.faw.cn',
        changeOrigin: true, // 支持跨域
        secure: false, // 支持 https
        pathRewrite: {
          '^/crs': '/crs',
        },
      },
      // 用户运营中心服务
      '/content': {
        target: 'https://uat-fc2-dyx-xyhyy.faw.cn/yhyy-center/api',
        changeOrigin: true, // 支持跨域
        secure: false, // 支持 https
        pathRewrite: {
          '^/content': '/',
        },
      }
    },
    // 请求代理配置 END

    // Various Dev Server settings
    host: HOST,
    port: PORT,
    autoOpenBrowser: true,

    // Use Eslint
    useEslint: true,

    // Source Maps
    devtool: 'eval-cheap-source-map',
    cssSourceMap: true,
  },

  build: {
    // Template for index.html
    index: resolve('dist/index.html'),

    // Paths
    assetsRoot: resolve('dist'),
    assetsSubDirectory: 'static',
    // must be app address on ModuleFederation
    assetsPublicPath: envConf.publicPath,

    // Source Maps
    productionSourceMap: false,
    devtool: 'source-map',

    // Gzip
    productionGzip: true,
    productionGzipExtensions: ['js', 'css'],
  },
};
