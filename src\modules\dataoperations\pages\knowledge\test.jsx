import React, { useState, useEffect } from 'react';
import { Button, Card, Space, message } from 'antd';
import {
  documentPage,
  knowledgeList,
  categoryTree
} from '../../api/knowledge';

/**
 * 知识库管理API测试页面
 * <AUTHOR>
 */
const KnowledgeTest = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState({});

  /**
   * 测试文档列表API
   * <AUTHOR>
   */
  const testDocumentPage = async () => {
    setLoading(true);
    try {
      const params = {
        current: 1,
        size: 10
      };
      
      console.log('测试文档列表API - 请求参数:', params);
      const response = await documentPage(params);
      console.log('测试文档列表API - 响应:', response);
      
      setResults(prev => ({
        ...prev,
        documentPage: response
      }));
      
      if (response.code === "200" || response.code === 200) {
        message.success(`文档列表API测试成功，返回${response.data?.records?.length || 0}条记录`);
      } else {
        message.error(`文档列表API测试失败: ${response.message}`);
      }
    } catch (error) {
      console.error('测试文档列表API错误:', error);
      message.error(`文档列表API测试异常: ${error.message}`);
      setResults(prev => ({
        ...prev,
        documentPage: { error: error.message }
      }));
    } finally {
      setLoading(false);
    }
  };

  /**
   * 测试知识库列表API
   * <AUTHOR>
   */
  const testKnowledgeList = async () => {
    setLoading(true);
    try {
      console.log('测试知识库列表API');
      const response = await knowledgeList({});
      console.log('测试知识库列表API - 响应:', response);
      
      setResults(prev => ({
        ...prev,
        knowledgeList: response
      }));
      
      if (response.code === "200" || response.code === 200) {
        message.success(`知识库列表API测试成功，返回${response.data?.length || 0}条记录`);
      } else {
        message.error(`知识库列表API测试失败: ${response.message}`);
      }
    } catch (error) {
      console.error('测试知识库列表API错误:', error);
      message.error(`知识库列表API测试异常: ${error.message}`);
      setResults(prev => ({
        ...prev,
        knowledgeList: { error: error.message }
      }));
    } finally {
      setLoading(false);
    }
  };

  /**
   * 测试类目树API
   * <AUTHOR>
   */
  const testCategoryTree = async () => {
    setLoading(true);
    try {
      console.log('测试类目树API');
      const response = await categoryTree({});
      console.log('测试类目树API - 响应:', response);
      
      setResults(prev => ({
        ...prev,
        categoryTree: response
      }));
      
      if (response.code === "200" || response.code === 200) {
        message.success(`类目树API测试成功，返回${response.data?.length || 0}条记录`);
      } else {
        message.error(`类目树API测试失败: ${response.message}`);
      }
    } catch (error) {
      console.error('测试类目树API错误:', error);
      message.error(`类目树API测试异常: ${error.message}`);
      setResults(prev => ({
        ...prev,
        categoryTree: { error: error.message }
      }));
    } finally {
      setLoading(false);
    }
  };

  /**
   * 测试所有API
   * <AUTHOR>
   */
  const testAllAPIs = async () => {
    await testDocumentPage();
    await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒
    await testKnowledgeList();
    await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒
    await testCategoryTree();
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card title="知识库管理API测试" style={{ marginBottom: '20px' }}>
        <Space wrap>
          <Button 
            type="primary" 
            onClick={testDocumentPage}
            loading={loading}
          >
            测试文档列表API
          </Button>
          <Button 
            onClick={testKnowledgeList}
            loading={loading}
          >
            测试知识库列表API
          </Button>
          <Button 
            onClick={testCategoryTree}
            loading={loading}
          >
            测试类目树API
          </Button>
          <Button 
            type="dashed"
            onClick={testAllAPIs}
            loading={loading}
          >
            测试所有API
          </Button>
        </Space>
      </Card>

      {Object.keys(results).length > 0 && (
        <Card title="API测试结果">
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            overflow: 'auto',
            maxHeight: '500px'
          }}>
            {JSON.stringify(results, null, 2)}
          </pre>
        </Card>
      )}
    </div>
  );
};

export default KnowledgeTest;
